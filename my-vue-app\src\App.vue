<script setup>
import { ref } from 'vue'
import LoginPage from './components/LoginPage.vue'
import SmsLoginPage from './components/SmsLoginPage.vue'
import RoleSelectionPage from './components/RoleSelectionPage.vue'
import SuccessPage from './components/SuccessPage.vue'
import HomePage from './components/HomePage.vue'
import EssayRequirements from './components/EssayRequirements.vue'
import EssayDetail from './components/EssayDetail.vue'
import ProfilePage from './components/ProfilePage.vue'
import EditProfilePage from './components/EditProfilePage.vue'
import ChangePhonePage from './components/ChangePhonePage.vue'
import VerificationCodePage from './components/VerificationCodePage.vue'
import HistoryPage from './components/HistoryPage.vue'
import MemberSelectionPage from './components/MemberSelectionPage.vue'
import AddAdultPage from './components/AddAdultPage.vue'
import AddStudentPage from './components/AddStudentPage.vue'
import MessageCenterPage from './components/MessageCenterPage.vue'
import TaskDetailPage from './components/TaskDetailPage.vue'


const currentPage = ref('login') // 'login', 'sms-login', 'role-selection', 'success', 'home', 'essay-requirements', 'essay-detail', 'profile', 'edit-profile', 'change-phone', 'verification-code', 'history', 'member-selection', 'message-center', 'task-detail'

const showSmsLogin = () => {
  currentPage.value = 'sms-login'
}

const showLogin = () => {
  currentPage.value = 'login'
}

const showRoleSelection = () => {
  currentPage.value = 'role-selection'
}

const showSuccess = () => {
  currentPage.value = 'success'
}

const showHome = () => {
  currentPage.value = 'home'
}

const showEssayRequirements = () => {
  currentPage.value = 'essay-requirements'
}

const showEssayDetail = () => {
  currentPage.value = 'essay-detail'
}

const handleRoleSelected = (role) => {
  console.log('用户选择的身份:', role)
  // 显示成功页面，然后跳转到首页
  showSuccess()
  setTimeout(() => {
    showHome()
  }, 2000) // 2秒后自动跳转到首页
}

const handleReturn = () => {
  // 从成功页面返回到登录页面
  showLogin()
}

const handleUploadEssay = (essayId) => {
  console.log('跳转到作文要求页面，作文ID:', essayId)
  showEssayRequirements()
}

const handleGoBackFromEssay = () => {
  showHome()
}

const handleEssayClick = (essayId) => {
  console.log('点击作文详情，作文ID:', essayId)
  showEssayDetail()
}

const handleGoBackFromDetail = () => {
  showHome()
}

const showProfile = () => {
  currentPage.value = 'profile'
}

const showHistory = () => {
  currentPage.value = 'history'
}

const showEditProfile = () => {
  currentPage.value = 'edit-profile'
}

const showChangePhone = () => {
  currentPage.value = 'change-phone'
}

const showVerificationCode = () => {
  currentPage.value = 'verification-code'
}

const showMemberSelection = () => {
  currentPage.value = 'member-selection'
}

const showAddAdult = () => {
  currentPage.value = 'add-adult'
}

const showAddStudent = () => {
  currentPage.value = 'add-student'
}

const showMessageCenter = () => {
  currentPage.value = 'message-center'
}

const showTaskDetail = () => {
  currentPage.value = 'task-detail'
}

const handleTabSwitch = (tab) => {
  console.log('切换到标签页:', tab)
  switch (tab) {
    case 'home':
      showHome()
      break
    case 'profile':
      showProfile()
      break
    case 'history':
      showHistory()
      break
    case 'data':
      console.log('跳转到成长数据页面')
      break
    default:
      console.log('未知标签页:', tab)
  }
}

const handleMenuClick = (action) => {
  console.log('菜单点击:', action)
  switch (action) {
    case 'message':
      console.log('跳转到消息中心')
      showMessageCenter()
      break
    case 'feedback':
      console.log('跳转到意见反馈')
      break
    case 'switch':
      console.log('切换身份')
      break
    case 'logout':
      console.log('退出登录')
      showLogin()
      break
    default:
      console.log('未知菜单操作:', action)
  }
}

const handleEditProfile = () => {
  console.log('跳转到编辑个人信息页面')
  showEditProfile()
}

const handleAddMember = () => {
  console.log('跳转到添加成员页面')
  showMemberSelection()
}

const handleGoBackFromEditProfile = () => {
  console.log('从编辑个人信息页面返回')
  showProfile()
}

const handleSaveProfile = () => {
  console.log('保存个人信息')
  // 这里可以添加保存逻辑
  showProfile()
}

const handleChangePhone = () => {
  console.log('跳转到更换手机号页面')
  showChangePhone()
}

const handleGoBackFromChangePhone = () => {
  console.log('从更换手机号页面返回')
  showEditProfile()
}

const handleNextStepFromChangePhone = () => {
  console.log('更换手机号下一步')
  showVerificationCode()
}

const handleGoBackFromVerification = () => {
  console.log('从验证码页面返回')
  showChangePhone()
}

const handleVerificationComplete = (code) => {
  console.log('验证码输入完成:', code)
  // 这里可以添加验证逻辑
  showEditProfile() // 验证成功后返回编辑页面
}

const handleResendCode = () => {
  console.log('重新发送验证码')
  // 这里可以添加重新发送验证码的逻辑
}

const handleGoBackFromMemberSelection = () => {
  console.log('从成员选择页面返回')
  showProfile()
}

const handleMemberSelected = (role) => {
  console.log('选择的成员身份:', role)
  // 这里可以添加处理选择身份的逻辑
  showProfile()
}

const handleWechatInvite = (role) => {
  console.log('微信邀请成员，身份:', role)
  // 这里可以添加微信邀请的逻辑
}

const handleManualAdd = (role) => {
  console.log('手动添加成员，身份:', role)

  // 根据身份类型判断是成人还是学生
  const adultRoles = ['mom', 'dad', 'grandpa', 'grandma', 'grandfather', 'grandmother']
  const studentRoles = ['baby', 'brother', 'sister', 'younger-brother', 'younger-sister']

  if (adultRoles.includes(role)) {
    // 跳转到成人添加页面
    showAddAdult()
  } else if (studentRoles.includes(role)) {
    // 跳转到学生添加页面
    showAddStudent()
  } else {
    console.log('未知身份类型:', role)
  }
}

const handleGoBackFromAddAdult = () => {
  console.log('从成人添加页面返回')
  showMemberSelection()
}

const handleGoBackFromAddStudent = () => {
  console.log('从学生添加页面返回')
  showMemberSelection()
}

const handleSaveAdult = (adultData) => {
  console.log('保存成人信息:', adultData)
  // 这里可以添加保存成人信息的逻辑
  showProfile() // 保存后返回个人资料页面
}

const handleSaveStudent = (studentData) => {
  console.log('保存学生信息:', studentData)
  // 这里可以添加保存学生信息的逻辑
  showProfile() // 保存后返回个人资料页面
}

const handleGoBackFromMessageCenter = () => {
  console.log('从消息中心返回')
  showProfile()
}

const handleGoBackFromTaskDetail = () => {
  console.log('从任务详情返回')
  showMessageCenter()
}

const handleMessageClick = () => {
  console.log('点击消息，跳转到任务详情')
  showTaskDetail()
}
</script>

<template>
  <div id="app">
    <LoginPage
      v-if="currentPage === 'login'"
      @sms-login="showSmsLogin"
    />
    <SmsLoginPage
      v-if="currentPage === 'sms-login'"
      @back="showLogin"
      @login-success="showRoleSelection"
    />
    <RoleSelectionPage
      v-if="currentPage === 'role-selection'"
      @back="showSmsLogin"
      @role-selected="handleRoleSelected"
    />
    <SuccessPage
      v-if="currentPage === 'success'"
      @return="handleReturn"
    />
    <HomePage
      v-if="currentPage === 'home'"
      @upload-essay="handleUploadEssay"
      @essay-click="handleEssayClick"
      @switch-tab="handleTabSwitch"
    />
    <EssayRequirements
      v-if="currentPage === 'essay-requirements'"
      @go-back="handleGoBackFromEssay"
    />
    <EssayDetail
      v-if="currentPage === 'essay-detail'"
      @go-back="handleGoBackFromDetail"
    />
    <ProfilePage
      v-if="currentPage === 'profile'"
      @switch-tab="handleTabSwitch"
      @menu-click="handleMenuClick"
      @edit-profile="handleEditProfile"
      @add-member="handleAddMember"
    />
    <EditProfilePage
      v-if="currentPage === 'edit-profile'"
      @go-back="handleGoBackFromEditProfile"
      @save-profile="handleSaveProfile"
      @change-phone="handleChangePhone"
    />
    <ChangePhonePage
      v-if="currentPage === 'change-phone'"
      @go-back="handleGoBackFromChangePhone"
      @next-step="handleNextStepFromChangePhone"
    />
    <VerificationCodePage
      v-if="currentPage === 'verification-code'"
      @go-back="handleGoBackFromVerification"
      @verification-complete="handleVerificationComplete"
      @resend-code="handleResendCode"
    />
    <HistoryPage
      v-if="currentPage === 'history'"
      @switch-tab="handleTabSwitch"
    />
    <MemberSelectionPage
      v-if="currentPage === 'member-selection'"
      @go-back="handleGoBackFromMemberSelection"
      @member-selected="handleMemberSelected"
      @wechat-invite="handleWechatInvite"
      @manual-add="handleManualAdd"
    />
    <AddAdultPage
      v-if="currentPage === 'add-adult'"
      @go-back="handleGoBackFromAddAdult"
      @save-adult="handleSaveAdult"
    />
    <AddStudentPage
      v-if="currentPage === 'add-student'"
      @go-back="handleGoBackFromAddStudent"
      @save-student="handleSaveStudent"
    />
    <MessageCenterPage
      v-if="currentPage === 'message-center'"
      @go-back="handleGoBackFromMessageCenter"
      @message-click="handleMessageClick"
    />
    <TaskDetailPage
      v-if="currentPage === 'task-detail'"
      @go-back="handleGoBackFromTaskDetail"
    />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

#app {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}
</style>
