<template>
  <div class="feedback-page">
    <!-- 状态栏占位 -->
    <div class="status-bar"></div>
    <!-- 顶部导航栏 -->
    <div class="header">
      <div class="header-left" @click="handleGoBack">
        <!-- 返回箭头 -->
        <img src="@/assets/feedback/arrow-left.svg" alt="返回" class="back-icon" />
        <img src="@/assets/feedback/arrow-left-2.svg" alt="返回" class="back-icon-2" />
      </div>
      <div class="header-center">
        <h1 class="page-title">意见反馈</h1>
      </div>
      <div class="header-right">
      </div>
    </div>

    <!-- 分隔线 -->
    <div class="divider"></div>

    <!-- 欢迎文本 -->
    <div class="welcome-section">
      <p class="greeting">亲爱的家长</p>
      <p class="description">使用中的吐槽和反馈，我们都听得到</p>
    </div>

    <!-- 分隔线 -->
    <div class="divider"></div>

    <!-- 反馈问题类型 -->
    <div class="feedback-type-section">
      <h3 class="section-title">反馈问题类型</h3>
      <div class="type-tags">
        <div 
          class="tag" 
          :class="{ active: selectedType === 'suggestion' }"
          @click="selectType('suggestion')"
        >
          优化建议
        </div>
        <div 
          class="tag" 
          :class="{ active: selectedType === 'bug' }"
          @click="selectType('bug')"
        >
          遇到BUG
        </div>
      </div>
    </div>

    <!-- 分隔线 -->
    <div class="divider"></div>

    <!-- 建议/BUG描述 -->
    <div class="description-section">
      <h3 class="section-title">建议/BUG描述</h3>
      <p class="section-subtitle">请描述具体建议/BUG发生过程，越详细超好哟～</p>
      <div class="input-container">
        <textarea 
          v-model="description"
          placeholder="请输入"
          class="description-input"
        ></textarea>
      </div>
    </div>

    <!-- 图片上传 -->
    <div class="upload-section">
      <h3 class="section-title">请上传相关图片</h3>
      <p class="section-subtitle">有效的图片信息有助于我们理解～</p>
      <div class="upload-area" @click="handleImageUpload">
        <img src="@/assets/feedback/upload-line-1.svg" alt="上传" class="upload-line-1" />
        <img src="@/assets/feedback/upload-line-2.svg" alt="上传" class="upload-line-2" />
      </div>
    </div>

    <!-- 提交按钮 -->
    <div class="submit-section">
      <button class="submit-button" @click="handleSubmit">
        提交
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 定义事件
const emit = defineEmits(['go-back', 'submit-feedback'])

// 响应式数据
const selectedType = ref('suggestion') // 默认选择优化建议
const description = ref('')

// 方法
const selectType = (type) => {
  selectedType.value = type
}

const handleImageUpload = () => {
  console.log('点击图片上传')
  // 这里可以添加图片上传逻辑
}

const handleSubmit = () => {
  if (!description.value.trim()) {
    alert('请输入反馈内容')
    return
  }
  
  const feedbackData = {
    type: selectedType.value,
    description: description.value,
    timestamp: new Date().toISOString()
  }
  
  console.log('提交反馈:', feedbackData)
  emit('submit-feedback', feedbackData)
}

const handleGoBack = () => {
  emit('go-back')
}
</script>

<style scoped>
.feedback-page {
  width: 390px;
  height: 844px;
  background: #FFFFFF;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  position: relative;
  overflow: hidden;
}

/* 状态栏占位 */
.status-bar {
  width: 100%;
  height: 44px;
  background: #FFFFFF;
}

/* 顶部导航栏 */
.header {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #F3F4F6;
  position: relative;
}

.header-left {
  width: 72px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #BCC1CA;
  position: relative;
  cursor: pointer;
}

.left-decoration-icons {
  position: absolute;
  left: 30px;
  top: 16.79px;
  width: 27.34px;
  height: 10.7px;
  z-index: 20;
}

.left-icon {
  position: absolute;
}

/* 左侧装饰图标定位 */
.icon-1 {
  left: 0px;
  top: 0px;
  width: 7.86px;
  height: 10.7px;
}

.icon-2 {
  left: 9.59px;
  top: 1.61px;
  width: 2.25px;
  height: 7.47px;
}

.icon-3 {
  left: 13.53px;
  top: 0.25px;
  width: 8.1px;
  height: 10.19px;
}

.icon-4 {
  left: 22.86px;
  top: 0.25px;
  width: 4.48px;
  height: 10.19px;
}

.back-icon {
  position: absolute;
  left: 3.4px;
  top: 12px;
  width: 17.2px;
  height: 0px;
  z-index: 5;
}

.back-icon-2 {
  position: absolute;
  left: 3.42px;
  top: 5.98px;
  width: 6.02px;
  height: 12.04px;
  z-index: 5;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.page-title {
  font-family: Inter;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #323842;
  margin: 0;
}

.header-right {
  width: 96px;
  height: 40px;
  border-left: 1px solid #BCC1CA;
  position: relative;
}

/* 根据Figma设计精确定位装饰图标 */
.icon-5 {
  left: 43.05px;
  top: 0px;
  width: 20.28px;
  height: 10.06px;
  opacity: 0.35;
}

.icon-6 {
  left: 64.68px;
  top: 3.75px;
  width: 1.19px;
  height: 3.59px;
  opacity: 0.4;
}

.icon-7 {
  left: 44.26px;
  top: 1.2px;
  width: 17.87px;
  height: 7.66px;
}

.icon-8 {
  left: 22.13px;
  top: 0.35px;
  width: 14.47px;
  height: 10.07px;
}

.icon-9 {
  left: 8.51px;
  top: 2.05px;
  width: 2.55px;
  height: 8.51px;
}

.icon-10 {
  left: 12.77px;
  top: 0.35px;
  width: 2.55px;
  height: 10.21px;
}

.icon-11 {
  left: 4.26px;
  top: 5.03px;
  width: 2.55px;
  height: 5.53px;
}

.icon-12 {
  left: 0px;
  top: 7.16px;
  width: 2.55px;
  height: 3.4px;
}

/* 分隔线 */
.divider {
  width: 375px;
  height: 4px;
  background: #F8F9FA;
  margin-left: 7px;
}

/* 欢迎文本 */
.welcome-section {
  padding: 28px 24px 18px;
}

.greeting {
  font-family: Inter;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #323842;
  margin: 0 0 10px 0;
}

.description {
  font-family: Inter;
  font-weight: 400;
  font-size: 12px;
  line-height: 22px;
  color: #323842;
  margin: 0;
}

/* 反馈问题类型 */
.feedback-type-section {
  padding: 18px 24px;
}

.section-title {
  font-family: Inter;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #323842;
  margin: 0 0 22px 0;
}

.type-tags {
  display: flex;
  gap: 24px;
}

.tag {
  height: 28px;
  border: 1px solid #636AE8;
  border-radius: 14px;
  padding: 4px 18px;
  font-family: Inter;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #636AE8;
  background: #FFFFFF;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag.active {
  background: #636AE8;
  color: #FFFFFF;
}

/* 建议/BUG描述 */
.description-section {
  padding: 18px 24px;
}

.section-subtitle {
  font-family: Inter;
  font-weight: 400;
  font-size: 10px;
  line-height: 16px;
  color: #9095A0;
  margin: 0 0 10px 0;
}

.input-container {
  position: relative;
}

.description-input {
  width: 100%;
  min-height: 60px;
  border: none;
  outline: none;
  resize: vertical;
  font-family: Inter;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #323842;
  background: transparent;
  padding: 10px 0;
}

.description-input::placeholder {
  color: #BCC1CA;
}

.description-input + .divider {
  margin-top: 10px;
}

/* 图片上传 */
.upload-section {
  padding: 18px 24px;
}

.upload-area {
  width: 56px;
  height: 56px;
  border: 1px dashed #DEE1E6;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0);
  position: relative;
  cursor: pointer;
  margin-top: 8px;
}

.upload-line-1 {
  position: absolute;
  left: 20px;
  top: 28px;
  width: 16px;
  height: 1px;
}

.upload-line-2 {
  position: absolute;
  left: 28px;
  top: 20px;
  width: 1px;
  height: 16px;
}

/* 提交按钮 */
.submit-section {
  position: absolute;
  bottom: 77px;
  left: 24px;
  right: 24px;
}

.submit-button {
  width: 100%;
  height: 52px;
  background: #636AE8;
  border: 1px solid rgba(0, 0, 0, 0);
  border-radius: 26px;
  box-shadow: 0px 4px 9px 0px rgba(99, 106, 232, 0.11), 0px 0px 2px 0px rgba(99, 106, 232, 0.12);
  font-family: Inter;
  font-weight: 400;
  font-size: 18px;
  line-height: 28px;
  color: #FFFFFF;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-button:hover {
  background: #5a61d9;
}

.submit-button:active {
  transform: translateY(1px);
}
</style>
